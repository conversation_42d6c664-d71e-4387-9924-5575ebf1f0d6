#include <gtest/gtest.h>
#include <chrono>
#include <thread>
#include <random>
#include <string>
#include <vector>
#include "raft-kv/raftCore/kvServer.h"
#include "raft-kv/raftCore/StreamingSnapshot.h"
#include "raft-kv/skipList/skipList.h"

/**
 * @brief 快照改进测试类
 * 
 * 测试快照触发机制和流式快照的性能改进
 */
class SnapshotImprovementsTest : public ::testing::Test
{
protected:
    void SetUp() override
    {
        // 初始化测试环境
        nodeId = 0;
        maxRaftState = 1000;
        
        // 创建测试用的跳表
        skipList = std::make_unique<SkipList<std::string, std::string>>(6);
        
        // 创建流式快照管理器
        snapshotManager = std::make_unique<StreamingSnapshotManager>(nodeId);
    }

    void TearDown() override
    {
        // 清理测试环境
        skipList.reset();
        snapshotManager.reset();
    }

    // 生成测试数据
    void GenerateTestData(size_t count)
    {
        std::random_device rd;
        std::mt19937 gen(rd());
        std::uniform_int_distribution<> dis(1000, 9999);

        for (size_t i = 0; i < count; ++i)
        {
            std::string key = "key_" + std::to_string(i);
            std::string value = "value_" + std::to_string(dis(gen));
            skipList->insert_element(key, value);
            lastRequestId["client_" + std::to_string(i % 10)] = static_cast<int>(i);
        }
    }

    // 验证数据完整性
    bool VerifyData(size_t expectedCount)
    {
        size_t actualCount = skipList->size();
        if (actualCount != expectedCount)
        {
            std::cout << "Size mismatch: expected " << expectedCount 
                      << ", actual " << actualCount << std::endl;
            return false;
        }

        // 验证部分数据
        for (size_t i = 0; i < std::min(expectedCount, size_t(100)); ++i)
        {
            std::string key = "key_" + std::to_string(i);
            std::string value;
            if (!skipList->search_element(key, value))
            {
                std::cout << "Missing key: " << key << std::endl;
                return false;
            }
        }

        return true;
    }

protected:
    int nodeId;
    int maxRaftState;
    std::unique_ptr<SkipList<std::string, std::string>> skipList;
    std::unique_ptr<StreamingSnapshotManager> snapshotManager;
    std::unordered_map<std::string, int> lastRequestId;
};

/**
 * @brief 测试流式快照的基本功能
 */
TEST_F(SnapshotImprovementsTest, BasicStreamingSnapshot)
{
    // 生成测试数据
    const size_t testDataCount = 1000;
    GenerateTestData(testDataCount);

    // 验证初始数据
    ASSERT_TRUE(VerifyData(testDataCount));

    // 创建流式快照
    std::string snapshotPath;
    auto startTime = std::chrono::high_resolution_clock::now();
    
    bool success = snapshotManager->CreateSnapshot(*skipList, lastRequestId, snapshotPath);
    
    auto endTime = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime);

    ASSERT_TRUE(success);
    ASSERT_FALSE(snapshotPath.empty());
    
    std::cout << "Created streaming snapshot in " << duration.count() << " ms" << std::endl;
    std::cout << "Snapshot path: " << snapshotPath << std::endl;

    // 清空原数据
    skipList->clear_all();
    lastRequestId.clear();
    ASSERT_EQ(skipList->size(), 0);

    // 从快照恢复数据
    startTime = std::chrono::high_resolution_clock::now();
    
    success = snapshotManager->RestoreSnapshot(snapshotPath, *skipList, lastRequestId);
    
    endTime = std::chrono::high_resolution_clock::now();
    duration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime);

    ASSERT_TRUE(success);
    
    std::cout << "Restored streaming snapshot in " << duration.count() << " ms" << std::endl;

    // 验证恢复的数据
    ASSERT_TRUE(VerifyData(testDataCount));

    // 清理临时文件
    StreamingSnapshotManager::CleanupTempFile(snapshotPath);
}

/**
 * @brief 测试大数据量的流式快照性能
 */
TEST_F(SnapshotImprovementsTest, LargeDataStreamingSnapshot)
{
    // 生成大量测试数据
    const size_t largeDataCount = 50000;
    
    auto startTime = std::chrono::high_resolution_clock::now();
    GenerateTestData(largeDataCount);
    auto endTime = std::chrono::high_resolution_clock::now();
    auto generateDuration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime);
    
    std::cout << "Generated " << largeDataCount << " entries in " 
              << generateDuration.count() << " ms" << std::endl;

    // 验证初始数据
    ASSERT_TRUE(VerifyData(largeDataCount));

    // 创建流式快照
    std::string snapshotPath;
    startTime = std::chrono::high_resolution_clock::now();
    
    bool success = snapshotManager->CreateSnapshot(*skipList, lastRequestId, snapshotPath);
    
    endTime = std::chrono::high_resolution_clock::now();
    auto snapshotDuration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime);

    ASSERT_TRUE(success);
    ASSERT_FALSE(snapshotPath.empty());
    
    std::cout << "Created large streaming snapshot in " << snapshotDuration.count() << " ms" << std::endl;

    // 检查快照文件大小
    std::ifstream file(snapshotPath, std::ios::binary | std::ios::ate);
    if (file.is_open())
    {
        auto fileSize = file.tellg();
        std::cout << "Snapshot file size: " << fileSize << " bytes" << std::endl;
        file.close();
    }

    // 清空原数据
    skipList->clear_all();
    lastRequestId.clear();

    // 从快照恢复数据
    startTime = std::chrono::high_resolution_clock::now();
    
    success = snapshotManager->RestoreSnapshot(snapshotPath, *skipList, lastRequestId);
    
    endTime = std::chrono::high_resolution_clock::now();
    auto restoreDuration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime);

    ASSERT_TRUE(success);
    
    std::cout << "Restored large streaming snapshot in " << restoreDuration.count() << " ms" << std::endl;

    // 验证恢复的数据
    ASSERT_TRUE(VerifyData(largeDataCount));

    // 性能断言：快照和恢复时间应该在合理范围内
    ASSERT_LT(snapshotDuration.count(), 10000); // 快照时间应小于10秒
    ASSERT_LT(restoreDuration.count(), 10000);  // 恢复时间应小于10秒

    // 清理临时文件
    StreamingSnapshotManager::CleanupTempFile(snapshotPath);
}

/**
 * @brief 测试跳表的遍历功能
 */
TEST_F(SnapshotImprovementsTest, SkipListTraversal)
{
    const size_t testDataCount = 100;
    GenerateTestData(testDataCount);

    // 使用遍历功能统计数据
    size_t traverseCount = 0;
    std::vector<std::pair<std::string, std::string>> traversedData;

    skipList->traverse([&](const std::string& key, const std::string& value) {
        traverseCount++;
        traversedData.emplace_back(key, value);
    });

    ASSERT_EQ(traverseCount, testDataCount);
    ASSERT_EQ(traversedData.size(), testDataCount);

    // 验证遍历的数据是有序的（跳表特性）
    for (size_t i = 1; i < traversedData.size(); ++i)
    {
        ASSERT_LT(traversedData[i-1].first, traversedData[i].first);
    }

    std::cout << "Successfully traversed " << traverseCount << " entries" << std::endl;
}

/**
 * @brief 测试跳表的清空功能
 */
TEST_F(SnapshotImprovementsTest, SkipListClearAll)
{
    const size_t testDataCount = 1000;
    GenerateTestData(testDataCount);

    ASSERT_EQ(skipList->size(), testDataCount);

    // 清空跳表
    skipList->clear_all();

    ASSERT_EQ(skipList->size(), 0);

    // 验证清空后无法找到任何数据
    std::string value;
    ASSERT_FALSE(skipList->search_element("key_0", value));
    ASSERT_FALSE(skipList->search_element("key_100", value));

    std::cout << "Successfully cleared skip list" << std::endl;
}

/**
 * @brief 性能基准测试：比较传统快照和流式快照
 */
TEST_F(SnapshotImprovementsTest, PerformanceBenchmark)
{
    const size_t benchmarkDataCount = 10000;
    GenerateTestData(benchmarkDataCount);

    // 测试流式快照性能
    std::string streamingSnapshotPath;
    auto startTime = std::chrono::high_resolution_clock::now();
    
    bool success = snapshotManager->CreateSnapshot(*skipList, lastRequestId, streamingSnapshotPath);
    
    auto endTime = std::chrono::high_resolution_clock::now();
    auto streamingDuration = std::chrono::duration_cast<std::chrono::microseconds>(endTime - startTime);

    ASSERT_TRUE(success);

    std::cout << "Streaming snapshot performance:" << std::endl;
    std::cout << "  Time: " << streamingDuration.count() << " microseconds" << std::endl;
    std::cout << "  Throughput: " << (benchmarkDataCount * 1000000.0 / streamingDuration.count()) 
              << " entries/second" << std::endl;

    // 清理
    StreamingSnapshotManager::CleanupTempFile(streamingSnapshotPath);
}

int main(int argc, char** argv)
{
    ::testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}
